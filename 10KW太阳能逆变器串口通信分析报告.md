# 10KW太阳能逆变器串口通信分析报告

## 项目概述
本报告详细分析了10KW太阳能逆变器项目中串口通信的所有打印信息，包括数据格式、发送频率和用途说明。

## 串口配置

### 串口A (SCIA)
- **波特率**: 115200
- **用途**: 调试通信和数据监控
- **初始化位置**: `main.c` 第174行
- **GPIO配置**: 通过 `InitSciaGpio()` 配置

### 串口B (SCIB)  
- **波特率**: 9600
- **用途**: 设备通信 (Modbus RTU协议)
- **初始化位置**: `main.c` 第175行
- **GPIO配置**: 通过 `InitScibGpio()` 配置

## 串口A打印信息详细分析

### 1. 系统初始化信息
```
Scia Init
```
- **发送时机**: 系统启动时
- **发送位置**: `User/SCI.c` 第175行
- **用途**: 确认串口A初始化完成

### 2. 系统消息队列
- **格式**: `\n消息内容\n`
- **发送机制**: 消息队列缓冲，每200ms检查并发送
- **实现位置**: `User/comm.c` 第85行
- **队列管理**: 使用环形缓冲区，防止消息丢失

### 3. 保护故障信息
```
EC[错误代码](错误描述)
```
- **示例**: `EC[1001](过压保护)`
- **发送时机**: 保护故障发生时
- **发送位置**: `User/comm.c` 第98行
- **发送频率**: 每200ms检查保护标志

### 4. 电流测量数据 (文本格式)
```
Iabc:A相电流,B相电流,C相电流;LC:功率因数
```
- **示例**: `Iabc:1523,1487,1501;LC:9950`
- **数据精度**: 电流值×100，功率因数×10000
- **发送位置**: `User/comm.c` 第130行
- **发送频率**: 每1秒交替发送

### 5. 完整系统数据包 (二进制格式)

#### 数据结构包含:
- **PV参数**: Vpv1, Ipv1, Vpv2, Ipv2
- **母线电压**: VBus, VBusPos, VBusNeg  
- **电网电压**: GVa, GVb, GVc (RMS值)
- **输出电流**: Ia, Ib, Ic (RMS值)
- **频率**: 电网频率
- **PWM占空比**: boost1_pwm, boost2_pwm
- **MPPT数据**: Vpvref_mpptOut1, Vpvref_mpptOut2
- **功率数据**: 
  - 输入功率: Input_Power_MPPT1/2/Total
  - 输出功率: Output_Power_a/b/c/T
- **系统状态**: 
  - 程序执行时间: tim_program_elapsed_time
  - 保护字: protect_word[0], protect_word[1]
  - 系统状态: solar_state

#### 发送参数:
- **发送位置**: `User/comm.c` 第177行
- **发送频率**: 每1秒
- **数据大小**: `sizeof(h.data) * 2` 字节

### 6. 波形数据 (二进制格式)

#### 包含波形:
1. **电网电压波形** (每相400字节)
   - Vgrid_a.adc_buf - A相电网电压
   - Vgrid_b.adc_buf - B相电网电压  
   - Vgrid_c.adc_buf - C相电网电压

2. **逆变器输出电压波形** (每相400字节)
   - Vinv_a.adc_buf - A相输出电压
   - Vinv_b.adc_buf - B相输出电压
   - Vinv_c.adc_buf - C相输出电压

3. **输出电流波形** (每相400字节)
   - Ia.adc_buf - A相输出电流
   - Ib.adc_buf - B相输出电流
   - Ic.adc_buf - C相输出电流

4. **调试数据缓冲区**
   - hdlog_buff: 1600字节 (4通道×100点×4字节)
   - data_buff: 3200字节 (8通道×100点×4字节)

#### 发送参数:
- **发送位置**: `User/comm.c` 第188-201行
- **发送频率**: 每1秒
- **总数据量**: 约8KB波形数据

### 7. 调试命令接收

#### 支持的命令格式:
```
SET:数值=>命令名称
```

#### 可用命令:
- `SET:1000=>POWER_TARGET` - 设置功率目标值
- `SET:1=>POWER_SW` - 设置功率开关状态
- `SET:1=>THETA_PLUS` - 增加相位补偿 (+0.001)
- `SET:1=>THETA_MINUS` - 减少相位补偿 (-0.001)

#### 实现位置:
- **命令解析**: `User/SCI.c` 第129-149行
- **参数设置**: 直接修改全局变量

## 串口B打印信息分析

### 1. 系统初始化信息
```
Scib Init
```
- **发送时机**: 系统启动时
- **发送位置**: `User/SCI.c` 第210行

### 2. Modbus RTU通信

#### 发送帧格式:
- **电流查询帧**: `{0x01, 0x03, 0x00, 0x0C, 0x00, 0x06, CRC_L, CRC_H}`
- **功率因数查询帧**: `{0x01, 0x03, 0x00, 0x30, 0x00, 0x02, CRC_L, CRC_H}`

#### 接收数据处理:
- **电流数据**: 17字节响应帧，包含三相电流浮点值
- **功率因数数据**: 9字节响应帧，包含功率因数浮点值
- **数据校验**: CRC16校验确保数据完整性

#### 实现位置:
- **发送函数**: `User/comm.c` 第310-332行
- **接收处理**: `User/comm.c` 第234-309行

## 数据发送时序

| 数据类型 | 发送频率 | 数据格式 | 数据量 |
|---------|---------|---------|--------|
| 初始化信息 | 启动时一次 | 文本 | <20字节 |
| 保护故障 | 故障时 | 文本 | <100字节 |
| 系统消息 | 200ms检查 | 文本 | 变长 |
| 电流数据 | 1秒 | 文本 | <50字节 |
| 系统数据包 | 1秒 | 二进制 | ~200字节 |
| 波形数据 | 1秒 | 二进制 | ~8KB |

## 数据解析建议

### 文本数据
- 直接使用串口调试工具查看
- 适合实时监控和调试

### 二进制数据  
- 需要专用上位机软件解析
- 建议使用C结构体对应解析
- 注意字节序和数据对齐

### 波形数据
- 每个采样点为16位整数
- 采样频率20kHz，每周期400点
- 可用于波形分析和故障诊断

## 应用场景

1. **系统调试**: 通过串口A监控系统状态和参数
2. **故障诊断**: 查看保护故障信息和波形数据
3. **性能分析**: 分析功率、电压、电流等运行参数
4. **参数调整**: 通过调试命令在线调整系统参数
5. **数据记录**: 记录长期运行数据用于分析优化

## 注意事项

1. **数据量大**: 每秒约8KB数据，注意串口缓冲区大小
2. **实时性**: 高频数据发送，确保接收端及时处理
3. **数据完整性**: 二进制数据传输注意校验
4. **调试安全**: 参数调整命令需谨慎使用
5. **兼容性**: 上位机软件需与数据结构保持一致

---
*报告生成时间: 2025年1月22日*  
*项目路径: ZK_10KW_PV_Inverter_v2*
